import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:latlong2/latlong.dart';
import 'package:location_picker_flutter_map/location_picker_flutter_map.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class LocationPickerBottomSheet extends StatelessWidget {
  const LocationPickerBottomSheet({super.key, this.initialLocation});

  final LatLng? initialLocation;

  @override
  Widget build(BuildContext context) {
    return FlutterLocationPicker(
      onPicked: (data) => context.pop(
        data.latLong.toLatLng(),
      ),
      initPosition: initialLocation != null
          ? LatLong(initialLocation!.latitude, initialLocation!.longitude)
          : null,
      selectedLocationButtonTextStyle: UITypography.button,
      mapLanguage: context.locale.languageCode,
      selectLocationButtonText: context.l10n.action_choose_position,
      searchBarHintText: context.l10n.action_search,
      trackMyPosition: true,
      selectLocationButtonStyle: context.theme.filledButtonTheme.style,
      markerIcon: const Icon(
        Icons.location_pin,
        color: UIColors.red,
        size: 40,
      ),
      userAgent: 'com.uab.sba',
    );
  }
}

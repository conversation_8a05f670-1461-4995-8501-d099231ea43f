import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/screen/error_screen.dart';
import 'package:sba/src/common/utils/stream_listenable.dart';
import 'package:sba/src/feature/auth/auth_router.dart';
import 'package:sba/src/feature/main/main_router.dart';

abstract final class AppRouter {
  static final GlobalKey<NavigatorState> rootNavigatorKey =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> shellNavigatorKey =
      GlobalKey<NavigatorState>();

  static GoRouter generateRouter(StreamListenable<bool> loggedStream) =>
      GoRouter(
        routes: [$signInRoute, $mainRoute],
        initialLocation: const HomeRoute().location,
        navigatorKey: rootNavigatorKey,
        errorBuilder: (context, state) => ErrorScreen(error: state.error),
        refreshListenable: loggedStream,
        redirect: (context, state) async {
          final loggedIn = await loggedStream.state;

          final goingToLogin =
              state.matchedLocation.contains(const SignInRoute().location);

          if (!loggedIn && !goingToLogin) {
            return const SignInRoute().location;
          }

          if (loggedIn && goingToLogin) {
            return const HomeRoute().location;
          }

          return null;
        },
      );
}

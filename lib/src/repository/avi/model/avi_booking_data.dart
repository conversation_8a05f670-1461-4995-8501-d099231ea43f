import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/avi/avi_booking_dto.dart';
import 'package:sba/src/repository/avi/model/avi_point_data.dart';
import 'package:sba/src/repository/avi/model/types/avi_service_type.dart';
import 'package:sba/src/repository/avi/model/types/avi_status.dart';
import 'package:sba/src/repository/avi/model/types/vehicle_category.dart';

@immutable
final class AviBookingData {
  const AviBookingData({
    required this.id,
    required this.state,
    required this.services,
    required this.date,
    required this.vehicleCategory,
    required this.slot,
    required this.plateNumber,
    required this.locationDetails,
  });

  factory AviBookingData.fromDto(AVIBookingDto dto) {
    return AviBookingData(
      id: dto.id,
      state: AviStatus.fromCode(dto.state),
      services: dto.services.map(AVIServiceType.fromCode).nonNulls.toList(),
      date: dto.date,
      vehicleCategory: VehicleCategory.fromCode(dto.vehicleCategory),
      slot: dto.slot,
      plateNumber: dto.plateNumber,
      locationDetails: AviPointData.fromDto(dto.locationDetails),
    );
  }

  factory AviBookingData.fake() => AviBookingData(
        id: '1',
        state: AviStatus.active,
        services: List.empty(),
        date: DateTime.now(),
        vehicleCategory: VehicleCategory.car,
        slot: '13:00',
        plateNumber: '',
        locationDetails: AviPointData.fake(),
      );

  final String id;
  final AviStatus? state;
  final List<AVIServiceType> services;
  final DateTime date;
  final VehicleCategory? vehicleCategory;
  final String slot;
  final String plateNumber;
  final AviPointData locationDetails;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is AviBookingData && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  AviBookingData copyWith({
    String? id,
    AviStatus? state,
    List<AVIServiceType>? services,
    DateTime? date,
    VehicleCategory? vehicleCategory,
    String? slot,
    String? plateNumber,
    AviPointData? locationDetails,
  }) {
    return AviBookingData(
      id: id ?? this.id,
      state: state ?? this.state,
      services: services ?? this.services,
      date: date ?? this.date,
      vehicleCategory: vehicleCategory ?? this.vehicleCategory,
      slot: slot ?? this.slot,
      plateNumber: plateNumber ?? this.plateNumber,
      locationDetails: locationDetails ?? this.locationDetails,
    );
  }
}

import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:sba/src/delivery/model/delivery_office.dart';
import 'package:sba/src/delivery/widget/office/office_delivery_wm.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/widget/form_builder_configured_typeahead.dart';

const _keys = (office: 'office',);

class OfficeDeliveryForm extends StatefulWidget {
  const OfficeDeliveryForm({super.key});

  @override
  State<OfficeDeliveryForm> createState() => OfficeDeliveryFormState();
}

class OfficeDeliveryFormState extends State<OfficeDeliveryForm> {
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  DeliveryOffice? saveAndValidate() {
    if (_formKey.currentState!.saveAndValidate()) {
      return _formKey.currentState!.value[_keys.office] as DeliveryOffice;
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return OfficeDeliveryWidget(formKey: _formKey);
  }
}

class OfficeDeliveryWidget
    extends ElementaryWidget<IOfficeDeliveryWidgetModel> {
  const OfficeDeliveryWidget({
    required this.formKey,
    Key? key,
    WidgetModelFactory wmFactory = defaultOfficeDeliveryWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final GlobalKey<FormBuilderState> formKey;

  @override
  Widget build(IOfficeDeliveryWidgetModel wm) {
    return Builder(
      builder: (context) => FormBuilder(
        key: formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            FormBuilderConfiguredTypeahead<DeliveryOffice>(
              name: _keys.office,
              asyncFilter: wm.findDelivery,
              textTransformer: (p) => p.name,
              hintTransformer: (p) => p.address,
              decoration: InputDecoration(
                labelText: context.l10n.form_office_speedy,
                hintText: context.l10n.form_office_hint,
              ),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

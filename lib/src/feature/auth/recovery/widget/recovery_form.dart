import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';

import 'package:sba/src/ui/theme/theme.dart';

typedef RecoveryFormData = ({String email});

typedef RecoverySubmitCallback = void Function(RecoveryFormData data);

class RecoveryForm extends StatelessWidget {
  RecoveryForm({required this.callback, super.key});

  final RecoverySubmitCallback callback;

  final _formKey = GlobalKey<FormBuilderState>();

  final _keys = (email: 'email');

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: UISpacing.defaultElementHorizontalPadding,
      child: FormBuilder(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            FormBuilderTextField(
              name: _keys.email,
              keyboardType: TextInputType.emailAddress,
              autocorrect: false,
              textInputAction: TextInputAction.done,
              decoration: InputDecoration(
                labelText: context.l10n.form_email,
                hintText: context.l10n.form_email_hint,
              ),
              validator: FormBuilderValidators.email(
                errorText: context.l10n.form_validation_email,
              ),
            ),
            const Gap(UISpacing.xl),
            FilledButton(
              onPressed: _onSubmit,
              child: Text(context.l10n.action_recover),
            ),
          ],
        ),
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      callback((email: _formKey.currentState!.value[_keys.email]));
    }
  }
}

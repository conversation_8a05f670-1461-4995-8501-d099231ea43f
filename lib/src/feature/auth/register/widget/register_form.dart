import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/common/utils/custom_field_validation.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/auth/model/register_data.dart';
import 'package:sba/src/repository/auth/model/type/profile_type.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/user/model/type/notification_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_configured_typeahead.dart';
import 'package:sba/src/ui/widget/form_builder_custom_checkbox_group.dart';
import 'package:sba/src/ui/widget/form_builder_custom_radio_group.dart';
import 'package:sba/src/ui/widget/form_builder_small_checkbox.dart';

typedef CheckEmail = Future<bool> Function(String email);

class RegisterForm extends StatelessWidget {
  RegisterForm({
    required this.onRegisterClick,
    required this.onSignInClick,
    required this.checkEmail,
    this.places,
    super.key,
  });

  final ValueChanged<RegisterData> onRegisterClick;
  final VoidCallback onSignInClick;
  final CheckEmail checkEmail;
  final List<Place>? places;

  final _formKey = GlobalKey<FormBuilderState>();

  static const _keys = (
    type: 'type',
    name: 'name',
    lastName: 'lastName',
    email: 'email',
    password: 'password',
    confirmPassword: 'confirmPassword',
    notification: 'notification',
    city: 'city',
    address: 'address',
    phone: 'phone',
    smsNotification: 'sms_notification',
    gdpr: 'gdpr',
    sba: 'sba',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderCustomRadioGroup<ProfileType>(
            label: context.l10n.form_type_hint,
            name: _keys.type,
            options: ProfileType.values
                .map(
                  (type) => FormBuilderFieldOption(
                    value: type,
                    child: Text(
                      type.localizedName(
                        context,
                      ),
                    ),
                  ),
                )
                .toList(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.name,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_name,
              hintText: context.l10n.form_name_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.lastName,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_last_name,
              hintText: context.l10n.form_last_name_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.email,
            keyboardType: TextInputType.emailAddress,
            autocorrect: false,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_email,
              hintText: context.l10n.form_email_hint,
            ),
            validator: FormBuilderValidators.email(
              errorText: context.l10n.form_validation_email,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.password,
            keyboardType: TextInputType.visiblePassword,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_password,
              hintText: context.l10n.form_password_hint,
            ),
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
              FormBuilderValidators.minLength(
                6,
                errorText: context.l10n.form_validation_min_length(6),
              ),
            ]),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.confirmPassword,
            keyboardType: TextInputType.visiblePassword,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_confirm_password,
              hintText: context.l10n.form_confirm_password_hint,
            ),
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
              FormBuilderValidators.minLength(
                6,
                errorText: context.l10n.form_validation_min_length(6),
              ),
              CustomValidators.passwordMatch(
                passwordField: () =>
                    _formKey.currentState!.fields[_keys.password]!,
                errorText: context.l10n.form_validation_password_match,
              ),
            ]),
          ),
          const Gap(UISpacing.l),
          FormBuilderCustomCheckboxGroup<NotificationType>(
            name: _keys.notification,
            label: context.l10n.form_notification_hint,
            initialValue: const [],
            options: NotificationType.values
                .map(
                  (type) => FormBuilderFieldOption(
                    value: type,
                    child: Text(
                      type.localizedName(context),
                    ),
                  ),
                )
                .toList(),
          ),
          const Gap(UISpacing.l),
          FormBuilderConfiguredTypeahead<Place>(
            name: _keys.city,
            data: places,
            filter: (p, suggestion) => p.name.containsIgnoreCase(suggestion),
            textTransformer: (p) => p.formattedText,
            decoration: InputDecoration(
              labelText: context.l10n.form_city,
              hintText: context.l10n.form_city_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.address,
            keyboardType: TextInputType.streetAddress,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_address,
              hintText: context.l10n.form_address_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.phone,
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_phone,
              hintText: context.l10n.form_phone_hint,
            ),
            validator: FormBuilderValidators.phoneNumber(
              errorText: context.l10n.form_validation_phone,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderCustomCheckboxGroup<NotificationType>(
            name: _keys.smsNotification,
            label: context.l10n.form_sms_hint,
            initialValue: const [],
            options: NotificationType.values
                .map(
                  (type) => FormBuilderFieldOption(
                    value: type,
                    child: Text(
                      type.localizedName(context),
                    ),
                  ),
                )
                .toList(),
          ),
          FormBuilderSmallCheckbox(
            name: _keys.gdpr,
            title: context.l10n.register_gdpr,
            expanded: true,
            validator: FormBuilderValidators.equal(
              true,
              errorText: context.l10n.form_validation_required,
            ),
          ),
          FormBuilderSmallCheckbox(
            name: _keys.sba,
            title: context.l10n.register_sba,
            expanded: true,
            validator: FormBuilderValidators.equal(
              true,
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: () => _onSubmit(context),
            child: Text(context.l10n.action_register),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                context.l10n.register_sign_in_hint,
                style: context.textTheme.bodyMedium,
              ),
              TextButton(
                onPressed: onSignInClick,
                child: Text(context.l10n.action_login),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _onSubmit(BuildContext context) async {
    if (_formKey.currentState!.saveAndValidate()) {
      if (!await checkEmail(
        _formKey.currentState!.value[_keys.email] as String,
      )) {
        _formKey.currentState?.fields[_keys.email]
            ?.invalidate(context.l10n.form_validation_email_taken);
        return;
      }

      onRegisterClick(
        RegisterData(
          firstName: _formKey.currentState!.value[_keys.name] as String,
          lastName: _formKey.currentState!.value[_keys.lastName] as String,
          email: _formKey.currentState!.value[_keys.email] as String,
          emailNotifications: (_formKey.currentState!.value[_keys.notification]
                  as List<NotificationType>)
              .toSet(),
          phone: _formKey.currentState!.value[_keys.phone] as String,
          phoneNotifications: (_formKey.currentState!
                  .value[_keys.smsNotification] as List<NotificationType>)
              .toSet(),
          city: _formKey.currentState!.value[_keys.city] as Place,
          address: _formKey.currentState!.value[_keys.address] as String,
          password: _formKey.currentState!.value[_keys.password] as String,
        ),
      );
    }
  }
}

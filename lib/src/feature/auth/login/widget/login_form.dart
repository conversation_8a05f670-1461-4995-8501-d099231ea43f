import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/auth/model/login_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_small_checkbox.dart';

class LoginForm extends StatelessWidget {
  LoginForm({
    required this.onLoginTap,
    required this.onForgottenPasswordTap,
    required this.onRegisterTap,
    required this.onLoginGuestTap,
    super.key,
  });

  final ValueChanged<LoginData> onLoginTap;
  final VoidCallback onForgottenPasswordTap;
  final VoidCallback onRegisterTap;
  final VoidCallback onLoginGuestTap;

  static const _keys = (
    email: 'email',
    password: 'password',
    rememberMe: 'remember_me',
  );

  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: UISpacing.defaultElementHorizontalPadding,
      child: FormBuilder(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            FormBuilderTextField(
              name: _keys.email,
              keyboardType: TextInputType.emailAddress,
              autocorrect: false,
              textInputAction: TextInputAction.next,
              decoration: InputDecoration(
                labelText: context.l10n.form_email,
                hintText: context.l10n.form_email_hint,
              ),
              validator: FormBuilderValidators.email(
                errorText: context.l10n.form_validation_email,
              ),
            ),
            const Gap(UISpacing.l),
            FormBuilderTextField(
              name: _keys.password,
              keyboardType: TextInputType.visiblePassword,
              textInputAction: TextInputAction.done,
              obscureText: true,
              decoration: InputDecoration(
                labelText: context.l10n.form_password,
                hintText: context.l10n.form_password_hint,
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(
                  errorText: context.l10n.form_validation_required,
                ),
                FormBuilderValidators.minLength(
                  6,
                  errorText: context.l10n.form_validation_min_length(6),
                ),
              ]),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: FormBuilderSmallCheckbox(
                    name: _keys.rememberMe,
                    initialValue: false,
                    title: context.l10n.login_remember_me,
                  ),
                ),
                TextButton(
                  onPressed: onForgottenPasswordTap,
                  child: Text(
                    context.l10n.login_forgotten_password,
                  ),
                ),
              ],
            ),
            const Gap(UISpacing.m),
            FilledButton(
              onPressed: _onSubmit,
              child: Text(context.l10n.action_login),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  context.l10n.login_register_hint,
                  style: context.textTheme.bodyMedium,
                ),
                TextButton(
                  onPressed: onRegisterTap,
                  child: Text(context.l10n.action_register),
                ),
              ],
            ),
            const MaxGap(54),
            OutlinedButton(
              onPressed: onLoginGuestTap,
              child: Text(context.l10n.login_guest_sign_in),
            ),
          ],
        ),
      ),
    );
  }

  void _onSubmit() {
    final valid = _formKey.currentState!.saveAndValidate();
    if (valid) {
      onLoginTap(
        LoginData(
          email: _formKey.currentState!.value[_keys.email] as String,
          password: _formKey.currentState!.value[_keys.password] as String,
          rememberMe: _formKey.currentState!.value[_keys.rememberMe] as bool,
        ),
      );
    }
  }
}

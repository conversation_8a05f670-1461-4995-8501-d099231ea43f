// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discount_routes.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$discountRoute];

RouteBase get $discountRoute => GoRouteData.$route(
  path: '/discounts',
  factory: _$DiscountRoute._fromState,
  routes: [
    GoRouteData.$route(
      path: '/details',
      factory: _$DiscountDetailsRoute._fromState,
    ),
  ],
);

mixin _$DiscountRoute on GoRouteData {
  static DiscountRoute _fromState(GoRouterState state) => const DiscountRoute();

  @override
  String get location => GoRouteData.$location('/discounts');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$DiscountDetailsRoute on GoRouteData {
  static DiscountDetailsRoute _fromState(GoRouterState state) =>
      DiscountDetailsRoute($extra: state.extra as DiscountItem);

  DiscountDetailsRoute get _self => this as DiscountDetailsRoute;

  @override
  String get location => GoRouteData.$location('/details');

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

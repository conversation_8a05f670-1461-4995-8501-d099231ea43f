import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/avi/model/search_avi_data.dart';
import 'package:sba/src/repository/avi/model/types/vehicle_category.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/form_builder_configured_typeahead.dart';
import 'package:sba/src/ui/widget/form_builder_custom_radio_group.dart';

class SearchCenterForm extends StatelessWidget {
  SearchCenterForm({
    required this.onSubmit,
    super.key,
    this.places,
  });

  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  final ValueChanged<SearchAviData> onSubmit;

  static const _keys = (
    city: 'city',
    category: 'category',
    hasLPG: 'has_lpg',
    motLPG: 'mot_lpg',
    date: 'date',
  );

  final List<Place>? places;

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderConfiguredTypeahead<Place>(
            name: _keys.city,
            data: places,
            filter: (p, suggestion) => p.name.containsIgnoreCase(suggestion),
            textTransformer: (p) => p.formattedText,
            decoration: InputDecoration(
              labelText: context.l10n.form_city,
              hintText: context.l10n.form_city_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<VehicleCategory>(
            name: _keys.category,
            items: VehicleCategory.generalValues
                .map(
                  (e) => DropdownMenuItem(
                    value: e,
                    child: Text(e.localizedName(context)),
                  ),
                )
                .toList(),
            decoration: InputDecoration(
              labelText: context.l10n.form_category,
              hintText: context.l10n.form_category_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderCustomRadioGroup<bool>(
            name: _keys.hasLPG,
            label: context.l10n.form_lpg_hint,
            wrapAlignment: WrapAlignment.start,
            options: [true, false]
                .map(
                  (e) => FormBuilderFieldOption(
                    value: e,
                    child: Text(
                      switch (e) {
                        true => context.l10n.form_type_option_yes,
                        false => context.l10n.form_type_option_no,
                      },
                    ),
                  ),
                )
                .toList(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderCustomRadioGroup<bool>(
            name: _keys.motLPG,
            label: context.l10n.form_mot_lpg_hint,
            wrapAlignment: WrapAlignment.start,
            options: [true, false]
                .map(
                  (e) => FormBuilderFieldOption(
                    value: e,
                    child: Text(
                      switch (e) {
                        true => context.l10n.form_type_option_yes,
                        false => context.l10n.form_type_option_no,
                      },
                    ),
                  ),
                )
                .toList(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDateTimePicker(
            name: _keys.date,
            locale: context.locale,
            textInputAction: TextInputAction.next,
            inputType: InputType.date,
            firstDate: DateTime.now(),
            decoration: InputDecoration(
              labelText: context.l10n.form_date,
              hintText: context.l10n.form_date_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_check),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      _formKey.currentContext?.focus.unfocus();
      onSubmit(
        SearchAviData(
          city: _formKey.currentState!.value[_keys.city] as Place,
          category: _formKey.currentState!.value[_keys.category] as VehicleCategory,
          hasLpg: _formKey.currentState!.value[_keys.hasLPG] as bool,
          newLpg: _formKey.currentState!.value[_keys.motLPG] as bool,
          date: _formKey.currentState!.value[_keys.date] as DateTime,
          gtp: true,
        ),
      );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/mot/request/mot_request_screen.dart';
import 'package:sba/src/feature/mot/view/mot_screen.dart';

class MotRoute extends GoRouteData with _$MotRoute {
  const MotRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const MotScreen();
  }
}

class MotRequestRoute extends GoRouteData with _$MotRequestRoute {
  const MotRequestRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const MotRequestScreen();
  }
}

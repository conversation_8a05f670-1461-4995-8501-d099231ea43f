import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/road_assistance/request_other/request_road_assistance_for_other_model.dart';
import 'package:sba/src/feature/road_assistance/request_other/request_road_assistance_for_other_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/road_assistance_reason.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:toastification/toastification.dart';

abstract interface class IRequestRoadAssistanceForOtherWidgetModel
    implements IWidgetModel {
  StateNotifier<List<CarBrand>?> get brands;

  StateNotifier<List<RoadAssistanceReason>?> get reasons;

  void onSubmit(RoadAssistanceRequest data);
}

RequestRoadAssistanceForOtherWidgetModel
    defaultRequestRoadAssistanceForOtherWidgetModelFactory(
  BuildContext context,
) {
  return RequestRoadAssistanceForOtherWidgetModel(
    RequestRoadAssistanceForOtherModel(
      errorHandler: get(),
      repository: get(),
      generalRepository: get(),
    ),
  );
}

class RequestRoadAssistanceForOtherWidgetModel extends WidgetModel<
        RequestRoadAssistanceForOtherScreen, RequestRoadAssistanceForOtherModel>
    implements IRequestRoadAssistanceForOtherWidgetModel {
  RequestRoadAssistanceForOtherWidgetModel(super.model);

  final _brands = StateNotifier<List<CarBrand>?>();
  final _reasons = StateNotifier<List<RoadAssistanceReason>?>();

  @override
  void initWidgetModel() async {
    _brands.accept(await model.brands);
    _reasons.accept(await model.reasons);
    super.initWidgetModel();
  }

  @override
  void onSubmit(RoadAssistanceRequest data) async {
    await context.showLoadingDialog();
    final result = await model.createRequest(data);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context
      ..showToast(
        type: ToastificationType.success,
        title: context.l10n.message_success_request,
      )
      ..pop();
  }

  @override
  StateNotifier<List<CarBrand>?> get brands => _brands;

  @override
  StateNotifier<List<RoadAssistanceReason>?> get reasons => _reasons;
}

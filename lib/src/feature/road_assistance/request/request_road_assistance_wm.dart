import 'dart:async';

import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:latlong2/latlong.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/road_assistance/request/request_road_assistance_model.dart';
import 'package:sba/src/feature/road_assistance/request/request_road_assistance_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/road_assistance_reason.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/modal/location_picker_bottom_sheet.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:toastification/toastification.dart';

abstract interface class IRequestRoadAssistanceWidgetModel
    implements IWidgetModel {
  StateNotifier<LatLng?> get preferredLocation;

  StateNotifier<UserData?> get user;

  StateNotifier<List<VehicleData>?> get vehicles;

  StateNotifier<List<CarBrand>?> get brands;

  StateNotifier<List<RoadAssistanceReason>?> get reasons;

  FutureOr<void> onPickOtherLocation();

  void onCurrentLocationChange(LatLng location);

  FutureOr<void> onSubmit(RoadAssistanceRequest data);
}

RequestRoadAssistanceWidgetModel defaultRequestRoadAssistanceWidgetModelFactory(
  BuildContext context,
) {
  return RequestRoadAssistanceWidgetModel(
    RequestRoadAssistanceModel(
      errorHandler: get(),
      generalRepository: get(),
      userRepository: get(),
      vehicleRepository: get(),
      repository: get(),
    ),
  );
}

class RequestRoadAssistanceWidgetModel
    extends WidgetModel<RequestRoadAssistanceScreen, RequestRoadAssistanceModel>
    implements IRequestRoadAssistanceWidgetModel {
  RequestRoadAssistanceWidgetModel(super.model);

  final _preferredLocation = StateNotifier<LatLng?>();
  final _user = StateNotifier<UserData?>();
  final _brands = StateNotifier<List<CarBrand>?>();
  final _vehicles = StateNotifier<List<VehicleData>?>();
  final _reasons = StateNotifier<List<RoadAssistanceReason>?>();
  LatLng? _currentLocation;

  @override
  void initWidgetModel() {
    _loadData();
    super.initWidgetModel();
  }

  void _loadData() async {
    _user.accept(await model.user);
    _brands.accept(await model.brands);
    _vehicles.accept(await model.vehicles);
    _reasons.accept(await model.reasons);
  }

  @override
  Future<void> onSubmit(RoadAssistanceRequest data) async {
    final coordinates = _preferredLocation.value ?? _currentLocation;

    if (coordinates == null) {
      await context.showMessageDialog(
        builder: (context) => MessageDialog.error(
          context: context,
          text: context.l10n.error_location_text,
        ),
      );

      return;
    }

    final requestData = data.copyWith(
      coordinates: coordinates,
    );

    await context.showLoadingDialog();
    final result = await model.createRequest(requestData);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context
      ..showToast(
        type: ToastificationType.success,
        title: context.l10n.message_success_request,
      )
      ..pop();
  }

  @override
  Future<void> onPickOtherLocation() async {
    final pickedLocation = await context.showModalBottomSheet<LatLng?>(
      builder: (context) => LocationPickerBottomSheet(
        initialLocation: _currentLocation,
      ),
    );

    _preferredLocation.accept(pickedLocation);
  }

  @override
  void onCurrentLocationChange(LatLng location) {
    _currentLocation = location;
  }

  @override
  StateNotifier<LatLng?> get preferredLocation => _preferredLocation;

  @override
  StateNotifier<UserData?> get user => _user;

  @override
  StateNotifier<List<CarBrand>?> get brands => _brands;

  @override
  StateNotifier<List<RoadAssistanceReason>?> get reasons => _reasons;

  @override
  StateNotifier<List<VehicleData>?> get vehicles => _vehicles;
}

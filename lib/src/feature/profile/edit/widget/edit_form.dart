import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/feature/profile/edit/edit_profile_model.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_configured_typeahead.dart';

class EditForm extends StatelessWidget {
  EditForm({required this.onSubmit, super.key, this.places, this.data});

  final ValueChanged<AddressData> onSubmit;

  final List<Place>? places;
  final UserData? data;
  final _formKey = GlobalKey<FormBuilderState>();

  static const _keys = (
    city: 'city',
    address: 'address',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            context.l10n.profile_edit_address_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderConfiguredTypeahead<Place>(
            name: _keys.city,
            initialValue: places?.firstWhereOrNull((i) => i.id == data?.city),
            data: places,
            filter: (p, suggestion) => p.name.containsIgnoreCase(suggestion),
            textTransformer: (p) => p.formattedText,
            decoration: InputDecoration(
              labelText: context.l10n.form_city,
              hintText: context.l10n.form_city_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.address,
            initialValue: data?.address,
            keyboardType: TextInputType.streetAddress,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_address,
              hintText: context.l10n.form_address_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_edit),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmit(
        (
          place: _formKey.currentState!.value[_keys.city] as Place,
          address: _formKey.currentState!.value[_keys.address] as String,
        ),
      );
    }
  }
}

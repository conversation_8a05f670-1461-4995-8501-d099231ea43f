import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/utils/custom_field_validation.dart';
import 'package:sba/src/feature/profile/password/change_password_model.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class PasswordForm extends StatelessWidget {
  PasswordForm({required this.onSubmit, super.key});

  final ValueChanged<PasswordData> onSubmit;

  final _formKey = GlobalKey<FormBuilderState>();

  static const _keys = (
    email: 'email',
    oldPassword: 'oldPassword',
    password: 'password',
    confirmPassword: 'confirmPassword',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _form<PERSON><PERSON>,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            context.l10n.profile_edit_personal_data_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.email,
            keyboardType: TextInputType.emailAddress,
            autocorrect: false,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_email,
              hintText: context.l10n.form_email_hint,
            ),
            validator: FormBuilderValidators.email(
              errorText: context.l10n.form_validation_email,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.oldPassword,
            keyboardType: TextInputType.visiblePassword,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_old_password,
              hintText: context.l10n.form_password_hint,
            ),
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
              FormBuilderValidators.minLength(
                6,
                errorText: context.l10n.form_validation_min_length(6),
              ),
            ]),
          ),
          const Gap(UISpacing.xl),
          Text(
            context.l10n.profile_edit_password_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.password,
            keyboardType: TextInputType.visiblePassword,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_new_password,
              hintText: context.l10n.form_password_hint,
            ),
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
              FormBuilderValidators.minLength(
                6,
                errorText: context.l10n.form_validation_min_length(6),
              ),
            ]),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.confirmPassword,
            keyboardType: TextInputType.visiblePassword,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_confirm_password,
              hintText: context.l10n.form_confirm_password_hint,
            ),
            validator: FormBuilderValidators.compose(
              [
                FormBuilderValidators.required(
                  errorText: context.l10n.form_validation_required,
                ),
                FormBuilderValidators.minLength(
                  6,
                  errorText: context.l10n.form_validation_min_length(6),
                ),
                CustomValidators.passwordMatch(
                  passwordField: () =>
                      _formKey.currentState!.fields[_keys.password]!,
                  errorText: context.l10n.form_validation_password_match,
                ),
              ],
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_edit),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmit(
        (
          email: _formKey.currentState!.value[_keys.email] as String,
          oldPassword:
              _formKey.currentState!.value[_keys.oldPassword] as String,
          newPassword: _formKey.currentState!.value[_keys.password] as String,
        ),
      );
    }
  }
}

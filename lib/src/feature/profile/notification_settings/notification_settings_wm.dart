import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/profile/notification_settings/notification_settings_model.dart';
import 'package:sba/src/feature/profile/notification_settings/notification_settings_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:toastification/toastification.dart';

abstract interface class INotificationSettingsWidgetModel
    implements IWidgetModel {
  StateNotifier<UserData?> get initialValue;

  Future<void> onSubmit(ConsentRecord record);
}

NotificationSettingsWidgetModel defaultNotificationSettingsWidgetModelFactory(
  BuildContext context,
) {
  return NotificationSettingsWidgetModel(
    NotificationSettingsModel(userRepository: get()),
  );
}

class NotificationSettingsWidgetModel
    extends WidgetModel<NotificationSettingsScreen, NotificationSettingsModel>
    implements INotificationSettingsWidgetModel {
  NotificationSettingsWidgetModel(super.model);

  final _initialValue = StateNotifier<UserData?>();

  @override
  void initWidgetModel() async {
    _initialValue.accept(await model.user);
    super.initWidgetModel();
  }

  @override
  StateNotifier<UserData?> get initialValue => _initialValue;

  @override
  Future<void> onSubmit(ConsentRecord record) async {
    await context.showLoadingDialog();
    final result = await model.update(record);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context
      ..showToast(
        type: ToastificationType.success,
        title: context.l10n.message_success_edit,
      )
      ..pop();
  }
}

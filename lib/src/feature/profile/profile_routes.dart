import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/profile/edit/edit_profile_screen.dart';
import 'package:sba/src/feature/profile/notification_settings/notification_settings_screen.dart';
import 'package:sba/src/feature/profile/password/change_password_screen.dart';
import 'package:sba/src/feature/profile/view/profile_screen.dart';

class ProfileRoute extends GoRouteData with _$ProfileRoute {
  const ProfileRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ProfileScreen();
  }
}

class EditProfileRoute extends GoRouteData with _$EditProfileRoute {
  const EditProfileRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const EditProfileScreen();
  }
}

class EditPasswordRoute extends GoRouteData with _$EditPasswordRoute {
  const EditPasswordRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ChangePasswordScreen();
  }
}

class NotificationSettingsRoute extends GoRouteData
    with _$NotificationSettingsRoute {
  const NotificationSettingsRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const NotificationSettingsScreen();
  }
}

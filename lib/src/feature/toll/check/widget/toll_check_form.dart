import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/feature/toll/check/toll_check_model.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/toll/model/toll_country.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';
import 'package:sba/src/ui/widget/form_builder_vehicle_picker.dart';
import 'package:sba/src/ui/widget/form_builder_visibility.dart';

class TollCheckForm extends StatelessWidget {
  TollCheckForm({
    required this.onSubmit,
    super.key,
    this.countries,
    this.vehicles,
  });

  final ValueChanged<TollCheckRecord> onSubmit;
  final List<TollCountry>? countries;

  final List<VehicleData>? vehicles;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  final ValueNotifier<bool> _showPlateNumber = ValueNotifier(false);

  static const _keys = (
    country: 'country',
    vehicle: 'vehicle',
    plate: 'plate',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderDropdown<TollCountry>(
            name: _keys.country,
            decoration: InputDecoration(
              labelText: context.l10n.form_country_vehicle,
              hintText: context.l10n.form_country_hint,
            ),
            items: countries
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.name),
                      ),
                    )
                    .toList() ??
                List.empty(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderVehiclePicker(
            name: _keys.vehicle,
            vehicles: vehicles,
            onOtherSelected: (e) => _showPlateNumber.value = e,
          ),
          FormBuilderVisibility(
            visible: _showPlateNumber,
            padding: const EdgeInsets.only(top: UISpacing.l),
            child: FormBuilderTextField(
              name: _keys.plate,
              decoration: InputDecoration(
                labelText: context.l10n.form_plate_number,
                hintText: context.l10n.form_plate_number_hint,
              ),
              textInputAction: TextInputAction.next,
              validator: FormBuilderValidators.licensePlate(
                errorText: context.l10n.form_validation_license_plate,
              ),
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_check),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      _formKey.currentContext?.focus.unfocus();

      onSubmit(
        (
          country: _formKey.currentState!.value[_keys.country] as TollCountry,
          plateNumber:
              (_formKey.currentState!.value[_keys.vehicle] as VehicleData?)
                      ?.plateNumber ??
                  _formKey.currentState!.value[_keys.plate] as String,
        ),
      );
    }
  }
}

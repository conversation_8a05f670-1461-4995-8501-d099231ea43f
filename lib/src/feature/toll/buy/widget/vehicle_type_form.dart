import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/toll/model/toll_category.dart';
import 'package:sba/src/repository/toll/model/toll_register_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_option_picker.dart';
import 'package:sba/src/ui/widget/option_box.dart';
import 'package:sba/src/ui/widget/value_listener.dart';

class VehicleTypeForm extends StatelessWidget {
  VehicleTypeForm({required this.onSubmitClick, super.key, this.initialData});

  final ValueChanged<TollCategory> onSubmitClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  final TollRegisterData? initialData;
  final ValueNotifier<bool> _buttonEnabled = ValueNotifier(false);

  static const _keys = (type: 'type',);

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      onChanged: () {
        _buttonEnabled.value = _formKey.currentState?.isValid ?? false;
      },
      child: ValueListener(
        value: initialData,
        onChange: (_) {
          _buttonEnabled.value = _formKey.currentState?.isValid ?? false;
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              context.l10n.toll_vehicle_type_label,
              style: context.textTheme.headlineMedium,
            ),
            const Gap(UISpacing.l),
            FormBuilderOptionPicker<TollCategory>(
              name: _keys.type,
              data: TollCategory.values,
              initialValue: initialData?.selectedCategory,
              elementBuilder: (type, selected) => SizedBox(
                height: 90,
                width: 150,
                child: OptionBox(
                  icon: type.icon,
                  text: type.localizedName(context),
                  selected: selected,
                ),
              ),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
            const Gap(UISpacing.xl),
            ValueListenableBuilder(
              valueListenable: _buttonEnabled,
              builder: (context, enabled, _) {
                return FilledButton(
                  onPressed: enabled ? _onSubmit : null,
                  child: Text(context.l10n.action_continue),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmitClick(_formKey.currentState!.value[_keys.type] as TollCategory);
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_option_picker.dart';
import 'package:sba/src/ui/widget/payment_tile_box.dart';

class PaymentTypeForm extends StatelessWidget {
  PaymentTypeForm({
    required this.onSubmitClick,
    required this.onBack,
    super.key,
  });

  final VoidCallback onSubmitClick;
  final VoidCallback onBack;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  final ValueNotifier<bool> _buttonEnabled = ValueNotifier(false);

  static const _keys = (type: 'type',);

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      onChanged: () {
        _buttonEnabled.value = _formKey.currentState?.isValid ?? false;
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            context.l10n.toll_payment_type_label,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderOptionPicker<String>(
            name: _keys.type,
            data: const ['a'],
            elementBuilder: (type, selected) =>
                PaymentTileBox.card(selected: selected),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xl),
          ValueListenableBuilder(
            valueListenable: _buttonEnabled,
            builder: (context, enabled, _) {
              return FilledButton(
                onPressed: enabled ? _onSubmit : null,
                child: Text(context.l10n.action_request),
              );
            },
          ),
          const Gap(UISpacing.m),
          FilledButton(
            onPressed: onBack,
            child: Text(context.l10n.action_back),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmitClick();
    }
  }
}

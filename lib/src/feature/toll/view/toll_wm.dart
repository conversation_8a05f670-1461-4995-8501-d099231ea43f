import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/toll/toll_routes.dart';
import 'package:sba/src/feature/toll/view/toll_model.dart';
import 'package:sba/src/feature/toll/view/toll_screen.dart';

abstract interface class ITollWidgetModel implements IWidgetModel {
  void onCheckTap();

  void onBuyTap();
}

TollWidgetModel defaultTollWidgetModelFactory(BuildContext context) {
  return TollWidgetModel(TollModel());
}

class TollWidgetModel extends WidgetModel<TollScreen, TollModel>
    implements ITollWidgetModel {
  TollWidgetModel(super.model);

  @override
  void onBuyTap() => const BuyTollRoute().go(context);

  @override
  void onCheckTap() => const CheckTollRoute().go(context);
}

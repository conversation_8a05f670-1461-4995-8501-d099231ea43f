import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/service_book/fuel/edit/edit_fuel_model.dart';
import 'package:sba/src/feature/service_book/fuel/edit/edit_fuel_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/fuel_trader.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:toastification/toastification.dart';

abstract interface class IEditFuelWidgetModel implements IWidgetModel {
  StateNotifier<RefuelData?> get initialValue;

  StateNotifier<List<FuelTrader>?> get fuels;

  void onDelete(RefuelData data);

  void onSubmit(RefuelData data);
}

EditFuelWidgetModel defaultEditFuelWidgetModelFactory(
  BuildContext context,
) {
  return EditFuelWidgetModel(
    EditFuelModel(
      serviceBookRepository: get(),
      generalRepository: get(),
      errorHandler: get(),
    ),
  );
}

class EditFuelWidgetModel extends WidgetModel<EditFuelScreen, EditFuelModel>
    implements IEditFuelWidgetModel {
  EditFuelWidgetModel(super.model);

  final _initialValue = StateNotifier<RefuelData?>();
  final _fuels = StateNotifier<List<FuelTrader>?>();

  @override
  void initWidgetModel() async {
    _initialValue.accept(widget.args.data);
    _fuels.accept(await model.fuels);

    super.initWidgetModel();
  }

  @override
  void onSubmit(RefuelData data) async {
    final value = data.copyWith(vehicleId: widget.args.vehicleId);

    await context.showLoadingDialog();
    final result = await model.createOrUpdate(value);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context
      ..showToast(
        type: ToastificationType.success,
        title: data.id == null
            ? context.l10n.message_success_create
            : context.l10n.message_success_edit,
      )
      ..pop();
  }

  @override
  void onDelete(RefuelData data) async {
    Future<void> delete(RefuelData data) async {
      await context.showLoadingDialog();
      final result = await model.deleteRefuel(data);
      context.hideLoadingDialog();

      if (result is Failure) {
        await context.showGeneralErrorDialog(failure: result);
        return;
      }

      context
        ..showToast(
          type: ToastificationType.success,
          title: context.l10n.message_success_delete,
        )
        ..pop();
    }

    await context.showMessageDialog(
      builder: (BuildContext context) => MessageDialog.action(
        type: MessageDialogType.error,
        title: context.l10n.message_delete_title,
        text: context.l10n.message_delete_text,
        primaryActionText: context.l10n.action_delete,
        secondaryActionText: context.l10n.action_cancel,
        primaryAction: () => delete(initialValue.value!),
      ),
    );
  }

  @override
  StateNotifier<RefuelData?> get initialValue => _initialValue;

  @override
  StateNotifier<List<FuelTrader>?> get fuels => _fuels;
}

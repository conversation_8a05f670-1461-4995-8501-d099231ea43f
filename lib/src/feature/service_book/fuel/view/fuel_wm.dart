import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/fuel/view/fuel_model.dart';
import 'package:sba/src/feature/service_book/fuel/view/fuel_screen.dart';
import 'package:sba/src/feature/service_book/service_book_routes.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';

abstract interface class IFuelWidgetModel implements IWidgetModel {
  EntityStateNotifier<List<RefuelData>> get refuels;

  void onAdd();

  void onEdit(RefuelData data);
}

FuelWidgetModel defaultFuelWidgetModelFactory(
  BuildContext context,
) {
  return FuelWidgetModel(
    FuelModel(errorHandler: get(), repository: get()),
  );
}

class FuelWidgetModel extends WidgetModel<FuelScreen, FuelModel>
    implements IFuelWidgetModel {
  FuelWidgetModel(super.model);

  final _refuels = EntityStateNotifier<List<RefuelData>>();

  @override
  void initWidgetModel() {
    _syncData();
    super.initWidgetModel();
  }

  void _syncData() async {
    _refuels.loading();
    final result = await model.getRefuelData(widget.args.vehicleId);

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      context.pop();
      return;
    }

    _refuels.content(
      result.maybeValue!.sorted(
        (a, b) => b.date.compareTo(a.date),
      ),
    );
  }

  @override
  EntityStateNotifier<List<RefuelData>> get refuels => _refuels;

  @override
  void onAdd() async {
    await EditRefuelRoute(vehicleId: widget.args.vehicleId).push<void>(context);

    _syncData();
  }

  @override
  void onEdit(RefuelData data) async {
    await EditRefuelRoute(
      vehicleId: widget.args.vehicleId,
      $extra: data,
    ).push<void>(context);

    _syncData();
  }
}

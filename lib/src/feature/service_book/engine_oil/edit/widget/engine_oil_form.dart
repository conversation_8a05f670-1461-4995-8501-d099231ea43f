import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/engine_oil_data.dart';
import 'package:sba/src/repository/service_book/model/types/vehicle_filter_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_custom_checkbox_group.dart';

class EngineOilForm extends StatelessWidget {
  EngineOilForm({
    required this.onSubmitClick,
    required this.onDeleteClick,
    super.key,
    this.initialValue,
  });

  final EngineOilData? initialValue;
  final ValueChanged<EngineOilData> onSubmitClick;
  final ValueChanged<EngineOilData> onDeleteClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  static const _keys = (
    date: 'date',
    service: 'service',
    name: 'name',
    mileage: 'mileage',
    quantity: 'quantity',
    filterTypes: 'filterTypes',
    price: 'price',
    notes: 'notes',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderDateTimePicker(
            name: _keys.date,
            initialValue: initialValue?.changeDate,
            locale: context.locale,
            textInputAction: TextInputAction.next,
            inputType: InputType.date,
            decoration: InputDecoration(
              labelText: context.l10n.form_date,
              hintText: context.l10n.form_date_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.service,
            initialValue: initialValue?.autoService,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_service,
              hintText: context.l10n.form_service_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.name,
            initialValue: initialValue?.oilType,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_oil,
              hintText: context.l10n.form_oil_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.quantity,
            initialValue: initialValue?.oilQuantity.toString(),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            textInputAction: TextInputAction.next,
            valueTransformer: (e) => num.tryParse(e ?? '')?.toDouble(),
            decoration: InputDecoration(
              labelText: context.l10n.form_quantity,
              hintText: context.l10n.form_quantity_hint,
            ),
            validator: FormBuilderValidators.numeric(
              errorText: context.l10n.form_validation_numeric,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.mileage,
            initialValue: initialValue?.mileage.toString(),
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.next,
            valueTransformer: (e) => num.tryParse(e ?? '')?.round(),
            decoration: InputDecoration(
              labelText: context.l10n.form_odometer,
              hintText: context.l10n.form_odometer_hint,
            ),
            validator: FormBuilderValidators.numeric(
              errorText: context.l10n.form_validation_numeric,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderCustomCheckboxGroup<VehicleFilterType>(
            name: _keys.filterTypes,
            label: context.l10n.form_vehicle_filter_hint,
            initialValue: initialValue?.changedFilters.toList() ?? [],
            options: VehicleFilterType.values
                .map(
                  (type) => FormBuilderFieldOption(
                    value: type,
                    child: Text(
                      type.localizedText(context),
                    ),
                  ),
                )
                .toList(),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.price,
            initialValue: initialValue?.price.toString(),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            textInputAction: TextInputAction.next,
            valueTransformer: (e) => num.tryParse(e ?? '')?.toDouble(),
            decoration: InputDecoration(
              labelText: context.l10n.form_price,
              hintText: context.l10n.form_price_hint,
            ),
            validator: FormBuilderValidators.numeric(
              errorText: context.l10n.form_validation_numeric,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.notes,
            initialValue: initialValue?.notes,
            maxLines: null,
            minLines: 4,
            keyboardType: TextInputType.multiline,
            decoration: InputDecoration(
              labelText: context.l10n.form_note,
              hintText: context.l10n.form_note_hint,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(
              initialValue != null
                  ? context.l10n.action_edit
                  : context.l10n.action_add,
            ),
          ),
          if (initialValue != null) const Gap(UISpacing.m),
          if (initialValue != null)
            FilledButton(
              onPressed: () => onDeleteClick(initialValue!),
              style: FilledButton.styleFrom(
                backgroundColor: context.theme.colorScheme.error,
              ),
              child: Text(context.l10n.action_delete),
            ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      final data = initialValue ?? EngineOilData.empty();

      onSubmitClick(
        data.copyWith(
          changeDate: _formKey.currentState!.value[_keys.date] as DateTime,
          autoService: _formKey.currentState!.value[_keys.service] as String,
          oilType: _formKey.currentState!.value[_keys.name] as String,
          oilQuantity: _formKey.currentState!.value[_keys.quantity] as double,
          mileage: _formKey.currentState!.value[_keys.mileage] as int,
          changedFilters: (_formKey.currentState!.value[_keys.filterTypes]
                  as List<VehicleFilterType>)
              .toSet(),
          price: _formKey.currentState!.value[_keys.price] as double,
          notes: _formKey.currentState!.value[_keys.notes] as String?,
        ),
      );
    }
  }
}

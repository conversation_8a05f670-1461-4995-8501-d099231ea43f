import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/other_service/view/other_service_model.dart';
import 'package:sba/src/feature/service_book/other_service/view/other_service_screen.dart';
import 'package:sba/src/feature/service_book/service_book_routes.dart';
import 'package:sba/src/repository/service_book/model/vehicle_service_data.dart';

abstract interface class IOtherServiceWidgetModel implements IWidgetModel {
  EntityStateNotifier<List<VehicleServiceData>> get service;

  void onAdd();

  void onEdit(VehicleServiceData data);
}

OtherServiceWidgetModel defaultOtherServiceWidgetModelFactory(
  BuildContext context,
) {
  return OtherServiceWidgetModel(
    OtherServiceModel(repository: get(), errorHandler: get()),
  );
}

class OtherServiceWidgetModel
    extends WidgetModel<OtherServiceScreen, OtherServiceModel>
    implements IOtherServiceWidgetModel {
  OtherServiceWidgetModel(super.model);

  final _service = EntityStateNotifier<List<VehicleServiceData>>();

  @override
  void initWidgetModel() {
    _syncData();
    super.initWidgetModel();
  }

  void _syncData() async {
    _service.loading();
    final result = await model.getVehicleService(widget.args.vehicleId);

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      context.pop();
      return;
    }

    _service.content(
      result.maybeValue!.sorted(
        (a, b) => b.serviceDate.compareTo(a.serviceDate),
      ),
    );
  }

  @override
  EntityStateNotifier<List<VehicleServiceData>> get service => _service;

  @override
  void onAdd() async {
    await EditOtherServiceRoute(vehicleId: widget.args.vehicleId)
        .push<void>(context);

    _syncData();
  }

  @override
  void onEdit(VehicleServiceData data) async {
    await EditOtherServiceRoute(
      vehicleId: widget.args.vehicleId,
      $extra: data,
    ).push<void>(context);

    _syncData();
  }
}

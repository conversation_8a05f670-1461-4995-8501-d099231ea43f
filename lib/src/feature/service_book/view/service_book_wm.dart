import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/service_book_routes.dart';
import 'package:sba/src/feature/service_book/view/service_book_model.dart';
import 'package:sba/src/feature/service_book/view/service_book_screen.dart';
import 'package:sba/src/feature/vehicle/vehicle_routes.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';

abstract interface class IServiceBookWidgetModel implements IWidgetModel {
  StateNotifier<List<VehicleData>> get vehicles;

  StateNotifier<VehicleData?> get selectedVehicle;

  StateNotifier<bool> get annualServiceWarning;

  void onAddVehicleTap();

  void onVehicleChange(VehicleData? data);

  void onServiceTap(VehicleData data);

  void onFuelTap(VehicleData data);

  void onMotTap(VehicleData data);
}

ServiceBookWidgetModel defaultServiceBookWidgetModelFactory(
  BuildContext context,
) {
  return ServiceBookWidgetModel(
    ServiceBookModel(
      vehicleRepository: get(),
      serviceRepository: get(),
      errorHandler: get(),
    ),
  );
}

class ServiceBookWidgetModel
    extends WidgetModel<ServiceBookScreen, ServiceBookModel>
    implements IServiceBookWidgetModel {
  ServiceBookWidgetModel(super.model);

  final _vehicles = StateNotifier<List<VehicleData>>();
  final _selectedVehicle = StateNotifier<VehicleData?>();

  final _annualInspectionWarning = StateNotifier<bool>();
  final _sub = CompositeSubscription();

  @override
  void initWidgetModel() {
    _sub.add(
      model.vehicles
          .asyncMap((result) async {
            if (result is Failure) {
              await context.showGeneralErrorDialog(
                failure: result as Failure,
              );
            }

            return result;
          })
          .map((e) => e.maybeValue)
          .listen((data) {
            _vehicles.accept(data);
            onVehicleChange(data?.firstOrNull);
          }),
    );
    super.initWidgetModel();
  }

  @override
  void dispose() {
    _sub.dispose();
    super.dispose();
  }

  @override
  void onAddVehicleTap() {
    const MyVehiclesRoute().go(context);
  }

  @override
  void onFuelTap(VehicleData data) =>
      RefuelsRoute(vehicleId: data.id ?? 0).go(context);

  @override
  void onServiceTap(VehicleData data) =>
      VehicleServicesRoute(vehicleId: data.id ?? 0).go(context);

  @override
  void onMotTap(VehicleData data) =>
      AnnualInspectionRoute(vehicleId: data.id ?? 0).go(context);

  @override
  void onVehicleChange(VehicleData? data) async {
    if (data == null) return;

    _selectedVehicle.accept(data);
    _annualInspectionWarning.accept(false);

    final ins = await model.lastInspection(vehicle: data);
    _annualInspectionWarning.accept(
      ins?.end.isLessThan(const Duration(days: 30)),
    );
  }

  @override
  StateNotifier<List<VehicleData>> get vehicles => _vehicles;

  @override
  StateNotifier<VehicleData?> get selectedVehicle => _selectedVehicle;

  @override
  StateNotifier<bool> get annualServiceWarning => _annualInspectionWarning;
}

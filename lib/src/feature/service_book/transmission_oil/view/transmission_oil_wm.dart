import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/service_book_routes.dart';
import 'package:sba/src/feature/service_book/transmission_oil/view/transmission_oil_model.dart';
import 'package:sba/src/feature/service_book/transmission_oil/view/transmission_oil_screen.dart';
import 'package:sba/src/repository/service_book/model/transmission_oil_data.dart';

abstract interface class ITransmissionOilWidgetModel implements IWidgetModel {
  EntityStateNotifier<List<TransmissionOilData>> get transmissionOil;

  void onAdd();

  void onEdit(TransmissionOilData data);
}

TransmissionOilWidgetModel defaultTransmissionOilWidgetModelFactory(
  BuildContext context,
) {
  return TransmissionOilWidgetModel(
    TransmissionOilModel(errorHandler: get(), repository: get()),
  );
}

class TransmissionOilWidgetModel
    extends WidgetModel<TransmissionOilScreen, TransmissionOilModel>
    implements ITransmissionOilWidgetModel {
  TransmissionOilWidgetModel(super.model);

  final _transmissionOil = EntityStateNotifier<List<TransmissionOilData>>();

  @override
  void initWidgetModel() {
    _syncData();
    super.initWidgetModel();
  }

  void _syncData() async {
    _transmissionOil.loading();
    final result = await model.getTransmissionOil(widget.args.vehicleId);

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      context.pop();
      return;
    }

    _transmissionOil.content(result.maybeValue!.sorted(
          (a, b) => b.changeDate.compareTo(a.changeDate),
    ),);
  }

  @override
  EntityStateNotifier<List<TransmissionOilData>> get transmissionOil =>
      _transmissionOil;

  @override
  void onAdd() async {
    await EditTransmissionOilChangeRoute(vehicleId: widget.args.vehicleId)
        .push<void>(context);

    _syncData();
  }

  @override
  void onEdit(TransmissionOilData data) async {
    await EditTransmissionOilChangeRoute(
      vehicleId: widget.args.vehicleId,
      $extra: data,
    ).push<void>(context);
    _syncData();
  }
}

import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/service_book/tires/buy/edit/edit_tire_model.dart';
import 'package:sba/src/feature/service_book/tires/buy/edit/edit_tire_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/tyre_brand.dart';
import 'package:sba/src/repository/general/model/tyre_size.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:toastification/toastification.dart';

abstract interface class IEditTireWidgetModel implements IWidgetModel {
  StateNotifier<TyreData?> get initialValue;

  StateNotifier<List<TyreBrand>?> get tires;

  StateNotifier<List<TyreSize>?> get tireWidths;

  StateNotifier<List<TyreSize>?> get tireHeights;

  StateNotifier<List<TyreSize>?> get tireDiameters;

  void onDelete(TyreData data);

  void onSubmit(TyreData data);
}

EditTireWidgetModel defaultEditTireWidgetModelFactory(
  BuildContext context,
) {
  return EditTireWidgetModel(
    EditTireModel(
      serviceBookRepository: get(),
      generalRepository: get(),
      errorHandler: get(),
    ),
  );
}

class EditTireWidgetModel extends WidgetModel<EditTireScreen, EditTireModel>
    implements IEditTireWidgetModel {
  EditTireWidgetModel(super.model);

  final _initialValue = StateNotifier<TyreData?>();
  final _tires = StateNotifier<List<TyreBrand>?>();

  final _tireWidths = StateNotifier<List<TyreSize>?>();
  final _tireHeights = StateNotifier<List<TyreSize>?>();
  final _tireDiameters = StateNotifier<List<TyreSize>?>();

  @override
  void initWidgetModel() async {
    _initialValue.accept(widget.args.data);
    _tires.accept(await model.tyreBrands);
    _tireWidths.accept(await model.tyreWidths);
    _tireHeights.accept(await model.tyreHeights);
    _tireDiameters.accept(await model.tyreDiameters);

    super.initWidgetModel();
  }

  @override
  void onSubmit(TyreData data) async {
    final value = data.copyWith(vehicleId: widget.args.vehicleId);

    await context.showLoadingDialog();
    final result = await model.createOrUpdate(value);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context
      ..showToast(
        type: ToastificationType.success,
        title: data.id == null
            ? context.l10n.message_success_create
            : context.l10n.message_success_edit,
      )
      ..pop();
  }

  @override
  void onDelete(TyreData data) async {
    Future<void> delete(TyreData data) async {
      await context.showLoadingDialog();
      final result = await model.deleteTyre(data);
      context.hideLoadingDialog();

      if (result is Failure) {
        await context.showGeneralErrorDialog(failure: result);
        return;
      }

      context
        ..showToast(
          type: ToastificationType.success,
          title: context.l10n.message_success_delete,
        )
        ..pop();
    }

    await context.showMessageDialog(
      builder: (BuildContext context) => MessageDialog.action(
        type: MessageDialogType.error,
        title: context.l10n.message_delete_title,
        text: context.l10n.message_delete_text,
        primaryActionText: context.l10n.action_delete,
        secondaryActionText: context.l10n.action_cancel,
        primaryAction: () => delete(initialValue.value!),
      ),
    );
  }

  @override
  StateNotifier<TyreData?> get initialValue => _initialValue;

  @override
  StateNotifier<List<TyreBrand>?> get tires => _tires;

  @override
  StateNotifier<List<TyreSize>?> get tireWidths => _tireWidths;

  @override
  StateNotifier<List<TyreSize>?> get tireHeights => _tireHeights;

  @override
  StateNotifier<List<TyreSize>?> get tireDiameters => _tireDiameters;
}

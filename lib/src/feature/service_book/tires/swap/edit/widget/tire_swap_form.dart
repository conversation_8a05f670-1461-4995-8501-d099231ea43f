import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class TireSwapForm extends StatelessWidget {
  TireSwapForm({
    required this.onSubmitClick,
    required this.onDeleteClick,
    super.key,
    this.initialValue,
    this.tires,
  });

  final TyreSwapData? initialValue;
  final List<TyreData>? tires;
  final ValueChanged<TyreSwapData> onSubmitClick;
  final ValueChanged<TyreSwapData> onDeleteClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  static const _keys = (
    tire: 'tire',
    date: 'date',
    service: 'service',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderDropdown<TyreData>(
            name: _keys.tire,
            initialValue: initialValue?.tyres,
            decoration: InputDecoration(
              labelText: context.l10n.form_tyre,
              hintText: context.l10n.form_tyre_hint,
            ),
            items: tires
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.fullName),
                      ),
                    )
                    .toList() ??
                List.empty(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDateTimePicker(
            name: _keys.date,
            initialValue: initialValue?.swapDate,
            locale: context.locale,
            textInputAction: TextInputAction.next,
            inputType: InputType.date,
            decoration: InputDecoration(
              labelText: context.l10n.form_date,
              hintText: context.l10n.form_date_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.service,
            initialValue: initialValue?.autoService,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_service,
              hintText: context.l10n.form_service_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(
              initialValue != null
                  ? context.l10n.action_edit
                  : context.l10n.action_add,
            ),
          ),
          if (initialValue != null) const Gap(UISpacing.m),
          if (initialValue != null)
            FilledButton(
              onPressed: () => onDeleteClick(initialValue!),
              style: FilledButton.styleFrom(
                backgroundColor: context.theme.colorScheme.error,
              ),
              child: Text(context.l10n.action_delete),
            ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      final data = initialValue ?? TyreSwapData.empty();
      onSubmitClick(
        data.copyWith(
          tyres: _formKey.currentState!.value[_keys.tire] as TyreData,
          swapDate: _formKey.currentState!.value[_keys.date] as DateTime,
          autoService: _formKey.currentState!.value[_keys.service] as String,
        ),
      );
    }
  }
}

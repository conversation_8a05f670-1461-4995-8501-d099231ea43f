import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/service_book/mot/edit/edit_mot_service_model.dart';
import 'package:sba/src/feature/service_book/mot/edit/edit_mot_service_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:toastification/toastification.dart';

abstract interface class IEditMotServiceWidgetModel implements IWidgetModel {
  StateNotifier<AnnualInspectionData?> get initialValue;

  void onDelete(AnnualInspectionData data);

  void onSubmit(AnnualInspectionData data);
}

EditMotServiceWidgetModel defaultEditMotServiceWidgetModelFactory(
    BuildContext context,) {
  return EditMotServiceWidgetModel(
    EditMotServiceModel(
      repository: get(),
      errorHandler: get(),
    ),
  );
}

class EditMotServiceWidgetModel
    extends WidgetModel<EditMotServiceScreen, EditMotServiceModel>
    implements IEditMotServiceWidgetModel {
  EditMotServiceWidgetModel(super.model);

  final _initialValue = StateNotifier<AnnualInspectionData?>();

  @override
  void initWidgetModel() {
    _initialValue.accept(widget.args.data);
    super.initWidgetModel();
  }

  @override
  void onSubmit(AnnualInspectionData data) async {
    final value = data.copyWith(vehicleId: widget.args.vehicleId);

    await context.showLoadingDialog();
    final result = await model.createOrUpdate(value);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context
      ..showToast(
        type: ToastificationType.success,
        title: data.id == null
            ? context.l10n.message_success_create
            : context.l10n.message_success_edit,
      )
      ..pop();
  }

  @override
  void onDelete(AnnualInspectionData data) async {
    Future<void> delete(AnnualInspectionData data) async {
      await context.showLoadingDialog();
      final result = await model.deleteAnnualInspection(data);
      context.hideLoadingDialog();

      if (result is Failure) {
        await context.showGeneralErrorDialog(failure: result);
        return;
      }

      context
        ..showToast(
          type: ToastificationType.success,
          title: context.l10n.message_success_delete,
        )
        ..pop();
    }

    await context.showMessageDialog(
      builder: (BuildContext context) => MessageDialog.action(
        type: MessageDialogType.error,
        title: context.l10n.message_delete_title,
        text: context.l10n.message_delete_text,
        primaryActionText: context.l10n.action_delete,
        secondaryActionText: context.l10n.action_cancel,
        primaryAction: () => delete(initialValue.value!),
      ),
    );
  }

  @override
  StateNotifier<AnnualInspectionData?> get initialValue => _initialValue;
}

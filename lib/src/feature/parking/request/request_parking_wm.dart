import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/parking/request/request_parking_model.dart';
import 'package:sba/src/feature/parking/request/request_parking_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';
import 'package:sba/src/repository/parking/model/parking_zone.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:toastification/toastification.dart';

abstract interface class IRequestParkingWidgetModel implements IWidgetModel {
  StateNotifier<List<ParkingZone>?> get parkingZones;

  StateNotifier<List<VehicleData>?> get vehicles;

  StateNotifier<ParkingRequest?> get initialData;

  void onSubmit(ParkingRequest data);
}

RequestParkingWidgetModel defaultRequestParkingWidgetModelFactory(
  BuildContext context,
) {
  return RequestParkingWidgetModel(
    RequestParkingModel(
      errorHandler: get(),
      repository: get(),
      vehicleRepository: get(),
      smsHandler: get(),
    ),
  );
}

class RequestParkingWidgetModel
    extends WidgetModel<RequestParkingScreen, RequestParkingModel>
    implements IRequestParkingWidgetModel {
  RequestParkingWidgetModel(super.model);

  final _parkingZones = StateNotifier<List<ParkingZone>?>();
  final _vehicles = StateNotifier<List<VehicleData>?>();
  final _initialData = StateNotifier<ParkingRequest?>();

  @override
  void initWidgetModel() async {
    _initialData.accept(widget.args.request);
    _parkingZones.accept(await model.parkingZones);
    _vehicles.accept(await model.vehicles);

    super.initWidgetModel();
  }

  @override
  void onSubmit(ParkingRequest data) async {
    final smsResult = await model.sendSms(context, data);

    if (smsResult.isFailure) {
      await context.showMessageDialog(
        builder: (context) => MessageDialog.error(
          context: context,
          text: context.l10n.error_sms,
        ),
      );

      return;
    }

    if (smsResult.isCancelled) {
      return;
    }

    await context.showLoadingDialog();
    final result = await model.createParkingRequest(data);

    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context
      ..showToast(
        type: ToastificationType.success,
        title: context.l10n.message_success_request,
      )
      ..pop();
  }

  @override
  StateNotifier<List<ParkingZone>?> get parkingZones => _parkingZones;

  @override
  StateNotifier<ParkingRequest?> get initialData => _initialData;

  @override
  StateNotifier<List<VehicleData>?> get vehicles => _vehicles;
}

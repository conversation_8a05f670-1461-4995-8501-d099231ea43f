import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';
import 'package:sba/src/repository/parking/model/parking_zone.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';
import 'package:sba/src/ui/widget/form_builder_configured_typeahead.dart';
import 'package:sba/src/ui/widget/form_builder_vehicle_picker.dart';
import 'package:sba/src/ui/widget/form_builder_visibility.dart';

class RequestParkingForm extends StatelessWidget {
  RequestParkingForm({
    required this.onSubmitClick,
    super.key,
    this.initialData,
    this.zones,
    this.vehicles,
  });

  final ParkingRequest? initialData;
  final List<ParkingZone>? zones;
  final List<VehicleData>? vehicles;
  final ValueChanged<ParkingRequest> onSubmitClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  final ValueNotifier<bool> _showPlateNumber = ValueNotifier(false);
  final ValueNotifier<ParkingZone?> _selectedParkingZone = ValueNotifier(null);

  static const _keys = (
    city: 'city',
    vehicle: 'vehicle',
    plateNumber: 'plate_number',
    zone: 'zone',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderConfiguredTypeahead<ParkingZone>(
            name: _keys.city,
            initialValue: initialData?.place,
            data: zones,
            filter: (p, suggestion) => p.city.containsIgnoreCase(suggestion),
            textTransformer: (p) => p.city,
            decoration: InputDecoration(
              labelText: context.l10n.form_city,
              hintText: context.l10n.form_city_choose_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
            onChanged: (z) => _selectedParkingZone.value = z,
          ),
          const Gap(UISpacing.l),
          FormBuilderVehiclePicker(
            name: _keys.vehicle,
            plateNumber: initialData?.plateNumber,
            vehicles: vehicles,
            onOtherSelected: (e) => _showPlateNumber.value = e,
          ),
          FormBuilderVisibility(
            visible: _showPlateNumber,
            padding: const EdgeInsets.only(top: UISpacing.l),
            child: FormBuilderTextField(
              name: _keys.plateNumber,
              decoration: InputDecoration(
                labelText: context.l10n.form_plate_number,
                hintText: context.l10n.form_plate_number_hint,
              ),
              textInputAction: TextInputAction.next,
              validator: FormBuilderValidators.licensePlate(
                errorText: context.l10n.form_validation_license_plate,
              ),
            ),
          ),
          const Gap(UISpacing.l),
          ValueListenableBuilder(
            valueListenable: _selectedParkingZone,
            builder: (context, zone, _) => FormBuilderDropdown<Zone>(
              name: _keys.zone,
              initialValue: zone != null ? null : initialData?.zone,
              decoration: InputDecoration(
                labelText: context.l10n.form_zone,
                hintText: context.l10n.form_zone_hint,
              ),
              items: (zone ?? initialData?.place)
                      ?.zones
                      .map(
                        (it) => DropdownMenuItem(
                          value: it,
                          child: Text(it.name),
                        ),
                      )
                      .toList() ??
                  List.empty(),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_send_sms),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmitClick(
        ParkingRequest(
          id: null,
          place: _formKey.currentState!.value[_keys.city] as ParkingZone,
          zone: _formKey.currentState!.value[_keys.zone] as Zone,
          plateNumber:
              (_formKey.currentState!.value[_keys.vehicle] as VehicleData?)
                      ?.plateNumber ??
                  _formKey.currentState!.value[_keys.plateNumber] as String,
          durationHours: 1,
          date: null,
        ),
      );
    }
  }
}

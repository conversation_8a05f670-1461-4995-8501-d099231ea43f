import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/form_builder_configured_typeahead.dart';

const _keys = (
  company: 'company',
  eik: 'eik',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  city: 'city',
  address: 'address',
  vehicle: 'vehicle',
);

typedef DataRecord = ({
  String? company,
  String? eik,
  String? firstName,
  String? lastName,
  String email,
  String phone,
  Place city,
  String address,
  VehicleData vehicle,
});

final GlobalKey<FormBuilderState> _formKey = GlobalKey();

class DataForm extends StatefulWidget {
  const DataForm({
    super.key,
    this.showCompanyData = false,
    this.data,
    this.cities,
    this.vehicles,
    this.vehicle,
  });

  final bool showCompanyData;
  final UserData? data;
  final List<Place>? cities;
  final List<VehicleData>? vehicles;
  final VehicleData? vehicle;

  @override
  State<DataForm> createState() => DataFormState();
}

class DataFormState extends State<DataForm> {
  DataRecord? saveAndValidate() {
    if (_formKey.currentState!.saveAndValidate()) {
      return (
        company: _formKey.currentState?.value[_keys.company],
        eik: _formKey.currentState?.value[_keys.eik],
        firstName: _formKey.currentState?.value[_keys.firstName],
        lastName: _formKey.currentState?.value[_keys.lastName],
        email: _formKey.currentState?.value[_keys.email],
        phone: _formKey.currentState?.value[_keys.phone],
        city: _formKey.currentState?.value[_keys.city],
        address: _formKey.currentState?.value[_keys.address],
        vehicle: _formKey.currentState?.value[_keys.vehicle],
      );
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        key: ValueKey(widget.data),
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (widget.showCompanyData)
            FormBuilderTextField(
              name: _keys.company,
              initialValue:
                  widget.data?.member?.companyName ?? widget.data?.companyName,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              enabled:
                  (widget.data?.member?.companyName ?? widget.data?.companyName)
                      .isNullOrEmpty,
              decoration: InputDecoration(
                labelText: context.l10n.form_company_name,
                hintText: context.l10n.form_company_name_hint,
              ),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            )
          else
            FormBuilderTextField(
              name: _keys.firstName,
              initialValue:
                  widget.data?.member?.firstName ?? widget.data?.firstName,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              enabled:
                  (widget.data?.member?.firstName ?? widget.data?.firstName)
                      .isNullOrEmpty,
              decoration: InputDecoration(
                labelText: context.l10n.form_name,
                hintText: context.l10n.form_name_hint,
              ),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
          const Gap(UISpacing.l),
          if (widget.showCompanyData)
            FormBuilderTextField(
              name: _keys.eik,
              initialValue:
                  widget.data?.member?.companyEIK ?? widget.data?.companyEIK,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.next,
              enabled:
                  (widget.data?.member?.companyEIK ?? widget.data?.companyEIK)
                      .isNullOrEmpty,
              decoration: InputDecoration(
                labelText: context.l10n.form_eik,
                hintText: context.l10n.form_eik_hint,
              ),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            )
          else
            FormBuilderTextField(
              name: _keys.lastName,
              initialValue:
                  widget.data?.member?.lastName ?? widget.data?.lastName,
              keyboardType: TextInputType.name,
              textInputAction: TextInputAction.next,
              enabled: (widget.data?.member?.lastName ?? widget.data?.lastName)
                  .isNullOrEmpty,
              decoration: InputDecoration(
                labelText: context.l10n.form_last_name,
                hintText: context.l10n.form_last_name_hint,
              ),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<VehicleData>(
            name: _keys.vehicle,
            initialValue: widget.vehicle,
            enabled: widget.vehicle == null,
            decoration: InputDecoration(
              labelText: context.l10n.form_vehicle,
              hintText: context.l10n.form_vehicle_hint,
            ),
            items: widget.vehicles
                    ?.map(
                      (data) => DropdownMenuItem(
                        value: data,
                        child: Text(data.plateNumber),
                      ),
                    )
                    .toList() ??
                [],
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.email,
            initialValue: widget.data?.member?.email ?? widget.data?.email,
            keyboardType: TextInputType.emailAddress,
            autocorrect: false,
            textInputAction: TextInputAction.next,
            enabled: (widget.data?.member?.email ?? widget.data?.email)
                .isNullOrEmpty,
            decoration: InputDecoration(
              labelText: context.l10n.form_email,
              hintText: context.l10n.form_email_hint,
            ),
            validator: FormBuilderValidators.email(
              errorText: context.l10n.form_validation_email,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.phone,
            initialValue: widget.data?.member?.phone ?? widget.data?.phone,
            keyboardType: TextInputType.phone,
            autocorrect: false,
            textInputAction: TextInputAction.next,
            enabled: (widget.data?.member?.phone ?? widget.data?.phone)
                .isNullOrEmpty,
            decoration: InputDecoration(
              labelText: context.l10n.form_phone,
              hintText: context.l10n.form_phone_hint,
            ),
            validator: FormBuilderValidators.phoneNumber(
              errorText: context.l10n.form_validation_phone,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderConfiguredTypeahead<Place>(
            name: _keys.city,
            initialValue: widget.cities?.firstWhereOrNull(
              (i) => i.id == (widget.data?.member?.city ?? widget.data?.city),
            ),
            data: widget.cities,
            enabled: widget.cities?.firstWhereOrNull(
                  (i) =>
                      i.id == (widget.data?.member?.city ?? widget.data?.city),
                ) ==
                null,
            filter: (p, suggestion) => p.name.containsIgnoreCase(suggestion),
            textTransformer: (p) => p.formattedText,
            decoration: InputDecoration(
              labelText: context.l10n.form_city,
              hintText: context.l10n.form_city_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.address,
            initialValue: widget.data?.member?.address ?? widget.data?.address,
            enabled: (widget.data?.member?.address ?? widget.data?.address)
                .isNullOrEmpty,
            keyboardType: TextInputType.streetAddress,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_address,
              hintText: context.l10n.form_address_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
        ],
      ),
    );
  }
}

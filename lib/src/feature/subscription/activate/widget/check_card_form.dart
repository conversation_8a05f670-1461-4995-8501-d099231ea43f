import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';

class CheckCardForm extends StatelessWidget {
  CheckCardForm({required this.onSubmitClick, super.key});

  final VoidCallback onSubmitClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  static const _keys = (
    cardNumber: 'cardNumber',
    barcode: 'barcode',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderTextField(
            name: _keys.cardNumber,
            decoration: InputDecoration(
              labelText: context.l10n.form_card_number,
              hintText: context.l10n.form_card_number_hint,
            ),
            textInputAction: TextInputAction.next,
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.barcode,
            decoration: InputDecoration(
              labelText: context.l10n.form_barcode,
              hintText: context.l10n.form_barcode_hint,
            ),
            textInputAction: TextInputAction.done,
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_add),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmitClick();
    }
  }
}

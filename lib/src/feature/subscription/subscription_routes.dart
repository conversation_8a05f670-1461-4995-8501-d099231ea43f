import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/subscription/activate/activate_subscription_screen.dart';
import 'package:sba/src/feature/subscription/buy/buy_subscription_screen.dart';
import 'package:sba/src/feature/subscription/details/subscription_details_screen.dart';
import 'package:sba/src/feature/subscription/view/subscription_screen.dart';

class SubscriptionRoute extends GoRouteData with _$SubscriptionRoute {
  const SubscriptionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SubscriptionScreen();
  }
}

class ActivateSubscriptionRoute extends GoRouteData
    with _$ActivateSubscriptionRoute {
  const ActivateSubscriptionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ActivateSubscriptionScreen();
  }
}

class BuySubscriptionRoute extends GoRouteData with _$BuySubscriptionRoute {
  const BuySubscriptionRoute({this.renewData});

  final int? renewData;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return BuySubscriptionScreen(
      args: BuySubscriptionScreenArgs(renewData: renewData),
    );
  }
}

class SubscriptionDetailsRoute extends GoRouteData
    with _$SubscriptionDetailsRoute {
  const SubscriptionDetailsRoute({required this.subscriptionId});

  final int subscriptionId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return SubscriptionDetailsScreen(
      args: SubscriptionDetailsScreenArgs(
        subscriptionId: subscriptionId,
      ),
    );
  }
}

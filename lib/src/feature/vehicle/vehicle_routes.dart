import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/feature/vehicle/edit/edit_vehicle_screen.dart';
import 'package:sba/src/feature/vehicle/view/vehicles_screen.dart';

class MyVehiclesRoute extends GoRouteData with _$MyVehiclesRoute {
  const MyVehiclesRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const VehiclesScreen();
  }
}

class NewVehicleRoute extends GoRouteData with _$NewVehicleRoute {
  const NewVehicleRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const EditVehicleScreen();
  }
}

class EditVehicleRoute extends GoRouteData with _$EditVehicleRoute {
  const EditVehicleRoute({required this.id});

  final int id;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditVehicleScreen(
      args: EditVehicleScreenArgs(id: id),
    );
  }
}

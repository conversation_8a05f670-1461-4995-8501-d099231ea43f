import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/app_router.dart';
import 'package:sba/src/feature/discounts/discount_routes.dart';
import 'package:sba/src/feature/information/information_routes.dart';
import 'package:sba/src/feature/legal_help/legal_help_routes.dart';
import 'package:sba/src/feature/main/home/<USER>';
import 'package:sba/src/feature/main/other/other_screen.dart';
import 'package:sba/src/feature/main/shell/shell_screen.dart';
import 'package:sba/src/feature/mot/mot_routes.dart';
import 'package:sba/src/feature/notification/notification_routes.dart';
import 'package:sba/src/feature/parking/parking_routes.dart';
import 'package:sba/src/feature/profile/profile_routes.dart';
import 'package:sba/src/feature/road_assistance/road_assistance_routes.dart';
import 'package:sba/src/feature/road_camera/road_camera_routes.dart';
import 'package:sba/src/feature/service_book/service_book_routes.dart';
import 'package:sba/src/feature/subscription/subscription_routes.dart';
import 'package:sba/src/feature/toll/toll_routes.dart';
import 'package:sba/src/feature/training_center/training_center_routes.dart';
import 'package:sba/src/feature/vehicle/vehicle_routes.dart';

part 'main_router.g.dart';

const home = '/home';
const roadAssistance = '/road_assistance';
const mot = '/mot';
const parking = '/parking';
const other = '/other';
const subscription = '/subscription';
const legalHelp = '/legalHelp';
const training = '/training';
const toll = '/toll';
const serviceBook = '/service_book';
const roadCameras = '/road_cameras';
const discounts = '/discounts';
const information = '/information';

@TypedShellRoute<MainRoute>(
  routes: [
    //home
    TypedGoRoute<HomeRoute>(
      path: home,
      routes: [
        TypedGoRoute<ProfileRoute>(
          path: 'profile',
          routes: [
            TypedGoRoute<EditProfileRoute>(path: 'edit'),
            TypedGoRoute<EditPasswordRoute>(path: 'password'),
            TypedGoRoute<NotificationSettingsRoute>(
              path: 'notification_settings',
            ),
            TypedGoRoute<MyVehiclesRoute>(
              path: 'vehicles',
              routes: [
                TypedGoRoute<NewVehicleRoute>(path: 'new'),
                TypedGoRoute<EditVehicleRoute>(path: 'edit'),
              ],
            ),
          ],
        ),
        TypedGoRoute<NotificationRoute>(
          path: 'notification',
        ),
        TypedGoRoute<SubscriptionDetailsRoute>(
          path: 'subscription_details',
        ),
      ],
    ),
    // parking
    TypedGoRoute<ParkingRoute>(
      path: parking,
      routes: [
        TypedGoRoute<RequestParkingRoute>(path: 'request'),
      ],
    ),
    // road assistance
    TypedGoRoute<RoadAssistanceRoute>(
      path: roadAssistance,
      routes: [
        TypedGoRoute<RequestRoadAssistanceRoute>(path: 'request'),
        TypedGoRoute<RequestRoadAssistanceOtherRoute>(path: 'request_other'),
      ],
    ),
    // toll
    TypedGoRoute<TollRoute>(
      path: toll,
      routes: [
        TypedGoRoute<CheckTollRoute>(path: 'check'),
        TypedGoRoute<BuyTollRoute>(path: 'buy'),
      ],
    ),
    // mot
    TypedGoRoute<MotRoute>(
      path: mot,
      routes: [
        TypedGoRoute<MotRequestRoute>(path: 'request'),
      ],
    ),
    // other
    TypedGoRoute<OtherRoute>(path: other),
    // subscription
    TypedGoRoute<SubscriptionRoute>(
      path: subscription,
      routes: [
        TypedGoRoute<ActivateSubscriptionRoute>(path: 'activate'),
        TypedGoRoute<BuySubscriptionRoute>(path: 'buy'),
      ],
    ),
    // training
    TypedGoRoute<TrainingCenterRoute>(path: training),
    // road cameras
    TypedGoRoute<RoadCameraRoute>(path: roadCameras),
    // legal help
    TypedGoRoute<LegalHelpRoute>(path: legalHelp),
    // discounts
    TypedGoRoute<DiscountRoute>(
      path: discounts,
      routes: [
        TypedGoRoute<DiscountDetailsRoute>(path: 'details'),
      ],
    ),
    // information
    TypedGoRoute<InformationRoute>(path: information),
    // service book
    TypedGoRoute<ServiceBookRoute>(
      path: serviceBook,
      routes: [
        TypedGoRoute<VehicleServicesRoute>(
          path: 'service',
          routes: [
            TypedGoRoute<EngineOilChangesRoute>(
              path: 'engine_oil',
              routes: [
                TypedGoRoute<EditEngineOilChangeRoute>(path: 'edit'),
              ],
            ),
            TypedGoRoute<TransmissionOilChangesRoute>(
              path: 'transmission_oil',
              routes: [
                TypedGoRoute<EditTransmissionOilChangeRoute>(path: 'edit'),
              ],
            ),
            TypedGoRoute<TyresRoute>(
              path: 'tyres',
              routes: [
                TypedGoRoute<NewTyresRoute>(
                  path: 'new_tyre',
                  routes: [
                    TypedGoRoute<EditNewTyreRoute>(path: 'edit'),
                  ],
                ),
                TypedGoRoute<TyreSwapsRoute>(
                  path: 'tyre_swap',
                  routes: [
                    TypedGoRoute<EditTyreSwapRoute>(path: 'edit'),
                  ],
                ),
              ],
            ),
            TypedGoRoute<OtherServicesRoute>(
              path: 'other',
              routes: [
                TypedGoRoute<EditOtherServiceRoute>(path: 'edit'),
              ],
            ),
          ],
        ),
        TypedGoRoute<RefuelsRoute>(
          path: 'fuel',
          routes: [
            TypedGoRoute<EditRefuelRoute>(path: 'edit'),
          ],
        ),
        TypedGoRoute<AnnualInspectionRoute>(
          path: 'annual_inspection',
          routes: [
            TypedGoRoute<EditAnnualInspectionRoute>(path: 'edit'),
          ],
        ),
      ],
    ),
  ],
)
class MainRoute extends ShellRouteData {
  const MainRoute();

  static final GlobalKey<NavigatorState> $navigatorKey =
      AppRouter.shellNavigatorKey;

  @override
  Widget builder(BuildContext context, GoRouterState state, Widget navigator) {
    return ShellScreen(
      navigator: navigator,
      state: state,
    );
  }
}

class HomeRoute extends GoRouteData with _$HomeRoute {
  const HomeRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const HomeScreen();
  }
}

class OtherRoute extends GoRouteData with _$OtherRoute {
  const OtherRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const OtherScreen();
  }
}

import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/feature/auth/auth_router.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/main/model/destination_type.dart';
import 'package:sba/src/feature/main/shell/shell_model.dart';
import 'package:sba/src/feature/main/shell/shell_screen.dart';
import 'package:sba/src/feature/main/shell/widget/shell_app_bar.dart';
import 'package:sba/src/feature/notification/notification_routes.dart';
import 'package:sba/src/feature/profile/profile_routes.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';

abstract interface class IShellWidgetModel implements IWidgetModel {
  StateNotifier<PreferredSizeWidget> get topBar;

  StateNotifier<List<DestinationType>> get destinations;

  StateNotifier<DestinationType?> get selectedDestination;

  StateNotifier<Widget> get navigator;

  Future<void> onRegisterTap();

  void onLayout(BuildContext context);

  void onBackTap();

  void onProfileTap();

  void onNotificationTap();

  void onNavigationTap(DestinationType type);
}

ShellWidgetModel defaultShellWidgetModelFactory(BuildContext context) {
  return ShellWidgetModel(
    ShellModel(
      authRepository: get(),
      userRepository: get(),
      vehicleRepository: get(),
      notificationRepository: get(),
      errorHandler: get(),
    ),
  );
}

class ShellWidgetModel extends WidgetModel<ShellScreen, ShellModel>
    implements IShellWidgetModel {
  ShellWidgetModel(super.model);

  final StateNotifier<PreferredSizeWidget> _topBar = StateNotifier();
  final StateNotifier<List<DestinationType>> _destinations = StateNotifier();
  final StateNotifier<DestinationType?> _selectedDestination = StateNotifier();
  final StateNotifier<Widget> _navigator = StateNotifier();
  final BehaviorSubject<BuildContext> _layoutContext = BehaviorSubject();
  final CompositeSubscription _subscription = CompositeSubscription();

  void _updatePassedState() {
    _selectedDestination.accept(
      DestinationType.values.firstWhereOrNull(
        (v) => widget.state.matchedLocation.contains(v.path),
      ),
    );
    _navigator.accept(widget.navigator);
  }

  @override
  void initWidgetModel() {
    _updatePassedState();
    _subscription
      ..add(
        CombineLatestStream.combine4(
          model.type,
          model.vehicleCount,
          model.unreadNotificationCount,
          _layoutContext.startWith(context),
          (type, vehicles, notifications, context) => (
            type: type,
            vehicles: vehicles,
            notifications: notifications,
            context: context
          ),
        )
            .map(
              (data) => switch (data.type) {
                UserType.normal => ShellAppBar.user(
                    notificationCount: data.notifications,
                    errorText: data.vehicles > 0
                        ? null
                        : context.l10n.message_profile_not_finished,
                    onNotificationTap: onNotificationTap,
                    onProfileTap: onProfileTap,
                    onBackTap: data.context.canPop() ? onBackTap : null,
                  ),
                UserType.guest => ShellAppBar.guest(
                    onRegisterTap: onRegisterTap,
                    onBackTap: data.context.canPop() ? onBackTap : null,
                  ),
              },
            )
            .listen(_topBar.accept),
      )
      ..add(
        model.type
            .map(
              (type) => switch (type) {
                UserType.normal => _normalDestinations,
                UserType.guest => _guestDestinations
              },
            )
            .listen(_destinations.accept),
      )
      ..add(
        Rx.timer<void>(null, const Duration(seconds: 10))
            .asyncMap((_) => model.requestNotificationPermission())
            .publish()
            .connect(),
      );
    super.initWidgetModel();
  }

  @override
  void didUpdateWidget(ShellScreen oldWidget) {
    _updatePassedState();
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _subscription.dispose();
    _layoutContext.close();
    super.dispose();
  }

  @override
  void onLayout(BuildContext context) => _layoutContext.add(context);

  @override
  void onBackTap() => _layoutContext.valueOrNull?.pop();

  @override
  Future<void> onRegisterTap() async {
    await model.logout();
    const RegisterRoute().go(context);
  }

  @override
  void onNavigationTap(DestinationType type) {
    context.go(type.path);
  }

  @override
  void onProfileTap() {
    const ProfileRoute().go(context);
  }

  @override
  void onNotificationTap() {
    const NotificationRoute().go(context);
  }

  @override
  StateNotifier<PreferredSizeWidget> get topBar => _topBar;

  @override
  StateNotifier<DestinationType?> get selectedDestination =>
      _selectedDestination;

  @override
  StateNotifier<Widget> get navigator => _navigator;

  @override
  StateNotifier<List<DestinationType>> get destinations => _destinations;

  static const _normalDestinations = [
    DestinationType.home,
    DestinationType.roadAssistance,
    DestinationType.parking,
    DestinationType.other,
  ];

  static const _guestDestinations = [
    DestinationType.home,
    DestinationType.roadAssistance,
    DestinationType.mot,
    DestinationType.other,
  ];
}
